/** @format */

'use client';

import { colors, gradients } from '@/app/colors';
import POICard from '@/app/shared/cards/components/POICard';
import { BasePOI } from '@/app/shared/cards/components/types';
import { usePOIFetcher } from '@/app/shared/hooks/usePOIFetcher';
import { usePOIManager } from '@/app/shared/maps/components/POIManager';
import UnifiedMapContainer from '@/app/shared/maps/components/UnifiedMapContainer';
import L from 'leaflet';
import { useRouter } from 'next/navigation';
import { useCallback, useEffect, useRef, useState } from 'react';
import { FaComments, FaHome, FaLocationArrow } from 'react-icons/fa';
import { FilterDropdown } from './components/FilterDropdown';

// Use the POI type from the maps component to match usePOIManager expectations
interface POI {
	id: number;
	poi_type: string;
	poi_id?: number;
	temp_id?: number;
	approved_id?: number;
	name: string;
	category: string;
	subcategory?: string;
	city?: string;
	district?: string;
	country?: string;
	latitude: number;
	longitude: number;
	random_score?: number;
	phone_number?: string;
	opening_hours?: string;
	description?: string;
	user_rating_avg?: number;
	user_rating_count?: number;
	distance_km?: number;
	is_favorite?: boolean;
	type?: string;
	neighborhood?: string;
	address?: string;
}

interface FlatMapComponentProps {
	userLocation: {
		latitude: number;
		longitude: number;
		accuracy?: number;
		timestamp: number;
		source: 'auto' | 'manual';
		label?: string;
		liveLocationEnabled?: boolean;
	} | null;
	currentLocation: { lat: number; lng: number } | null;
	onLocationChange: (location: { lat: number; lng: number }) => void;
	showInfo: boolean;
}

export function FlatMapComponent({
	userLocation,
	currentLocation,
	onLocationChange,
	showInfo,
}: FlatMapComponentProps) {
	const router = useRouter();
	const [map, setMap] = useState<L.Map | null>(null);
	const [selectedPOI, setSelectedPOI] = useState<BasePOI | null>(null);
	const [showPOIInfo, setShowPOIInfo] = useState(false);
	const [mapCenter, setMapCenter] = useState<{
		lat: number;
		lng: number;
	} | null>(currentLocation);
	const [mapZoom, setMapZoom] = useState<number>(15);
	const [filters, setFilters] = useState<{
		categories: string[];
		subcategories: string[];
	}>({
		categories: [],
		subcategories: [],
	});

	// Track last fetch position to avoid unnecessary fetches
	const lastFetchCenter = useRef<{ lat: number; lng: number } | null>(null);
	const lastFetchZoom = useRef<number | null>(null);

	// Handle POI click
	const handlePOIClick = useCallback((poi: POI) => {
		const basePOI: BasePOI = {
			id: poi.id,
			name: String(poi.name),
			category: String(poi.category || ''),
			city: poi.city ? String(poi.city) : undefined,
			district: poi.district ? String(poi.district) : undefined,
			neighborhood: poi.neighborhood ? String(poi.neighborhood) : undefined,
			subcategory: poi.subcategory ? String(poi.subcategory) : undefined,
			latitude: typeof poi.latitude === 'number' ? poi.latitude : 0,
			longitude: typeof poi.longitude === 'number' ? poi.longitude : 0,
			poi_type: poi.poi_type,
			poi_id: poi.poi_id,
			temp_id: poi.temp_id,
			approved_id: poi.approved_id,
			phone_number: poi.phone_number ? String(poi.phone_number) : undefined,
			opening_hours: poi.opening_hours ? String(poi.opening_hours) : undefined,
			description: poi.description ? String(poi.description) : undefined,
			user_rating_avg: poi.user_rating_avg,
			user_rating_count: poi.user_rating_count,
			distance_km: poi.distance_km,
			is_favorite: poi.is_favorite,
		};

		setSelectedPOI(basePOI);
		setShowPOIInfo(true);
	}, []);

	// Use optimized POI fetcher (like the old flat-map)
	const {
		pois,
		isLoading: isPOILoading,
		fetchPOIs,
		clearPOIs,
		loadingProgress,
	} = usePOIFetcher();

	// Handle filter changes
	const handleFilterChange = useCallback(
		(newFilters: { categories: string[]; subcategories: string[] }) => {
			console.log('Filter changed, clearing POIs and fetching new data', {
				oldFilters: filters,
				newFilters,
				currentPOICount: pois.length,
			});
			setFilters(newFilters);
			// Clear existing POIs immediately when filters change
			clearPOIs();
			console.log('POIs cleared, should be empty now');
			// Reset fetch tracking to force new fetch
			lastFetchCenter.current = null;
			lastFetchZoom.current = null;
			// Refetch POIs with new filters
			if (mapCenter && map) {
				const bounds = map.getBounds();
				lastFetchCenter.current = mapCenter;
				lastFetchZoom.current = mapZoom;
				console.log('Fetching POIs with new filters:', newFilters);
				fetchPOIs(bounds, mapZoom, newFilters);
			}
		},
		[mapCenter, map, mapZoom, fetchPOIs, clearPOIs, filters, pois.length]
	);

	// Use optimized POI manager (like the old flat-map)
	const { updateMarkers, cleanup } = usePOIManager({
		map,
		pois,
		onPOIClick: handlePOIClick,
		userLocation,
	});

	// Update markers when POIs or map changes
	useEffect(() => {
		if (map) {
			console.log('Updating POI markers on flat-map:', pois.length);
			updateMarkers();
		}
	}, [map, pois, updateMarkers]);

	// Cleanup on unmount
	useEffect(() => {
		return () => {
			cleanup();
		};
	}, [cleanup]);

	// Update map center when current location changes
	useEffect(() => {
		if (currentLocation) {
			setMapCenter(currentLocation);
		}
	}, [currentLocation]);

	// Update map center when user location changes (if no current location set)
	useEffect(() => {
		if (userLocation && !currentLocation) {
			const newCenter = {
				lat: userLocation.latitude,
				lng: userLocation.longitude,
			};
			setMapCenter(newCenter);
			onLocationChange(newCenter);
		}
	}, [userLocation, currentLocation, onLocationChange]);

	// Initial POI loading when map is ready and we have a location (only once)
	useEffect(() => {
		if (
			map &&
			mapCenter &&
			pois.length === 0 &&
			!isPOILoading &&
			!lastFetchCenter.current
		) {
			console.log('Initial POI loading for flat-map');
			const bounds = map.getBounds();
			lastFetchCenter.current = mapCenter;
			lastFetchZoom.current = mapZoom;
			// Only pass filters if they're actually active
			const hasActiveFilters =
				filters.categories.length > 0 || filters.subcategories.length > 0;
			fetchPOIs(bounds, mapZoom, hasActiveFilters ? filters : undefined);
		}
	}, [map, mapCenter, pois.length, isPOILoading, fetchPOIs, mapZoom]);

	// Calculate distance between two points in kilometers
	const calculateDistance = useCallback(
		(lat1: number, lng1: number, lat2: number, lng2: number) => {
			const R = 6371; // Earth's radius in kilometers
			const dLat = ((lat2 - lat1) * Math.PI) / 180;
			const dLng = ((lng2 - lng1) * Math.PI) / 180;
			const a =
				Math.sin(dLat / 2) * Math.sin(dLat / 2) +
				Math.cos((lat1 * Math.PI) / 180) *
					Math.cos((lat2 * Math.PI) / 180) *
					Math.sin(dLng / 2) *
					Math.sin(dLng / 2);
			const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
			return R * c;
		},
		[]
	);

	// Fetch POIs when map bounds change with movement threshold
	const handleBoundsChange = useCallback(
		(bounds: L.LatLngBounds, center: L.LatLng, zoom: number) => {
			const newCenter = { lat: center.lat, lng: center.lng };
			setMapCenter(newCenter);
			setMapZoom(zoom);
			onLocationChange(newCenter);

			// Check if we need to fetch new POIs based on movement threshold
			const shouldFetch = (() => {
				// Always fetch if no previous fetch
				if (!lastFetchCenter.current || !lastFetchZoom.current) {
					return true;
				}

				// Fetch if zoom level changed significantly
				if (Math.abs(zoom - lastFetchZoom.current) >= 1) {
					return true;
				}

				// Calculate movement distance
				const distance = calculateDistance(
					lastFetchCenter.current.lat,
					lastFetchCenter.current.lng,
					newCenter.lat,
					newCenter.lng
				);

				// Movement threshold based on zoom level
				// Higher zoom = smaller threshold (more sensitive to movement)
				// Lower zoom = larger threshold (less sensitive to movement)
				const threshold =
					zoom > 15 ? 0.1 : zoom > 12 ? 0.2 : zoom > 10 ? 0.5 : 1.0; // km

				return distance > threshold;
			})();

			if (shouldFetch) {
				console.log(
					'Significant movement detected, fetching POIs for new area'
				);
				lastFetchCenter.current = newCenter;
				lastFetchZoom.current = zoom;
				// Always pass filters for movement-based fetching to maintain filter state
				fetchPOIs(bounds, zoom, filters);
			} else {
				console.log('Minor movement, skipping POI fetch');
			}
		},
		[onLocationChange, fetchPOIs, filters, calculateDistance]
	);

	const handleMapMove = useCallback(
		(center: { lat: number; lng: number }, zoom: number) => {
			setMapCenter(center);
			setMapZoom(zoom);
			onLocationChange(center);
		},
		[onLocationChange]
	);

	const handleGoToUserLocation = () => {
		if (userLocation) {
			const newCenter = {
				lat: userLocation.latitude,
				lng: userLocation.longitude,
			};
			setMapCenter(newCenter);
			setMapZoom(15);
			onLocationChange(newCenter);
		}
	};

	return (
		<div
			className='fixed inset-0 w-full h-full z-50'
			style={{ overflow: 'hidden' }}>
			{/* Unified Map Container */}
			{mapCenter && (
				<UnifiedMapContainer
					mapId='flat-map'
					center={[mapCenter.lat, mapCenter.lng]}
					zoom={mapZoom}
					onMapReady={setMap}
					onBoundsChange={handleBoundsChange}
					style={{
						position: 'absolute',
						top: 0,
						left: 0,
						right: 0,
						bottom: 0,
						overflow: 'hidden',
					}}
				/>
			)}

			{/* Debug panel - top left (like the old flat-map) */}
			<div
				className='absolute top-4 left-4 z-50 p-3 text-xs md:text-sm font-mono'
				style={{
					background: 'rgba(0, 0, 0, 0.8)',
					color: 'white',
					backdropFilter: 'blur(4px)',
				}}>
				<div>POIs: {pois.length}</div>
				<div>
					Center:{' '}
					{mapCenter
						? `${mapCenter.lat.toFixed(4)}, ${mapCenter.lng.toFixed(4)}`
						: 'None'}
				</div>
				<div>Zoom: {mapZoom}</div>
				<div>Loading: {isPOILoading ? 'Yes' : 'No'}</div>
				<div>Progress: {Math.round(loadingProgress)}%</div>
				<div>
					Filters: {filters.categories.length + filters.subcategories.length}
					{(filters.categories.length > 0 ||
						filters.subcategories.length > 0) &&
						' (ACTIVE)'}
				</div>
			</div>

			{/* Filter Dropdown - top right */}
			<div className='absolute top-4 right-4 z-50'>
				<FilterDropdown
					onFilterChange={handleFilterChange}
					currentFilters={filters}
				/>
			</div>

			{/* Control and Navigation buttons - reorganized for better hierarchy (like old flat-map) */}
			<div className='absolute bottom-4 right-4 z-50 flex flex-col gap-2'>
				{/* Primary Control - Return to Globe */}
				<button
					onClick={() => router.push('/globe')}
					className='px-6 py-3 font-medium transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105 flex items-center gap-2'
					style={{
						background: gradients.primary,
						color: 'white',
						border: 'none',
					}}
					title='Return to Globe View'>
					<FaLocationArrow
						className='w-4 h-4'
						style={{ transform: 'rotate(180deg)' }}
					/>
					<span className='text-sm font-medium'>Return to Globe</span>
				</button>

				{/* Secondary Controls */}
				{userLocation && (
					<button
						onClick={handleGoToUserLocation}
						className='px-6 py-3 font-medium transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105 flex items-center gap-2'
						style={{
							background: gradients.secondary,
							color: 'white',
							border: 'none',
						}}
						title='Focus on your location'>
						<FaLocationArrow className='w-4 h-4' />
						<span className='text-sm font-medium hidden sm:inline'>
							My Location
						</span>
					</button>
				)}

				{/* Navigation buttons - placed below primary controls */}
				<div
					className='flex flex-col gap-2 pt-2 border-t'
					style={{ borderColor: colors.ui.gray200 }}>
					<button
						onClick={() => router.push('/')}
						className='px-6 py-3 font-medium transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105 flex items-center gap-2'
						style={{
							background: colors.ui.gray100,
							color: colors.neutral.textBlack,
							border: `1px solid ${colors.ui.gray200}`,
						}}
						title='Home'>
						<FaHome className='w-4 h-4' />
						<span className='text-sm font-medium hidden sm:inline'>Home</span>
					</button>

					<button
						onClick={() => router.push('/chat')}
						className='px-6 py-3 font-medium transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105 flex items-center gap-2'
						style={{
							background: colors.ui.gray100,
							color: colors.neutral.textBlack,
							border: `1px solid ${colors.ui.gray200}`,
						}}
						title='Chat'>
						<FaComments className='w-4 h-4' />
						<span className='text-sm font-medium hidden sm:inline'>Chat</span>
					</button>
				</div>
			</div>

			{/* POI Info Panel - Moved to bottom left */}
			{showPOIInfo && selectedPOI && (
				<div className='absolute bottom-4 left-4 md:w-96 z-40'>
					<POICard
						poi={selectedPOI}
						isVisible={showPOIInfo}
						onClose={() => {
							setShowPOIInfo(false);
							setSelectedPOI(null);
						}}
					/>
				</div>
			)}

			{/* Info Panel */}
			{showInfo && (
				<div
					className='absolute top-4 left-4 right-4 md:left-auto md:right-4 md:w-96 z-30 shadow-xl p-4 md:p-6 lg:p-8'
					style={{
						background: 'rgba(255, 255, 255, 0.9)',
						backdropFilter: 'blur(8px)',
						border: `1px solid ${colors.ui.gray200}`,
					}}>
					<h3
						className='text-lg md:text-xl lg:text-2xl font-semibold mb-2'
						style={{ color: colors.neutral.textBlack }}>
						Flat Map View
					</h3>
					<p
						className='text-sm md:text-base mb-4'
						style={{ color: colors.neutral.slateGray }}>
						Explore locations on a traditional 2D map. Click on markers to see
						details about places of interest.
					</p>
					<div className='space-y-2 text-xs md:text-sm'>
						<div style={{ color: colors.neutral.slateGray }}>
							• Click and drag to pan the map
						</div>
						<div style={{ color: colors.neutral.slateGray }}>
							• Use mouse wheel to zoom in/out
						</div>
						<div style={{ color: colors.neutral.slateGray }}>
							• Click markers to view place details
						</div>
						<div style={{ color: colors.neutral.slateGray }}>
							• Use the location button to return to your position
						</div>
					</div>
				</div>
			)}

			{/* Loading Indicator */}
			{isPOILoading && (
				<div className='absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50'>
					<div
						className='shadow-lg p-4 flex items-center gap-3'
						style={{
							background: 'rgba(255, 255, 255, 0.9)',
							backdropFilter: 'blur(8px)',
							border: `1px solid ${colors.ui.gray200}`,
						}}>
						<div
							className='animate-spin h-5 w-5 border-2 border-t-transparent'
							style={{ borderColor: colors.brand.primary }}></div>
						<span
							className='text-sm md:text-base'
							style={{ color: colors.neutral.textBlack }}>
							Loading places...
						</span>
					</div>
				</div>
			)}
		</div>
	);
}
