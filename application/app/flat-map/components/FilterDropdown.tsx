/** @format */

'use client';

import { colors } from '@/app/colors';
import { getAllCategoryColors } from '@/app/flat-map/marker-colors';
import {
	getPOICategories,
	getPOISubcategories,
} from '@/app/shared/poi/constants';
import { useCallback, useEffect, useRef, useState } from 'react';
import { FaFilter, FaTimes } from 'react-icons/fa';

interface FilterState {
	categories: string[];
	subcategories: string[];
}

interface FilterDropdownProps {
	onFilterChange: (filters: FilterState) => void;
	currentFilters: FilterState;
}

export function FilterDropdown({
	onFilterChange,
	currentFilters,
}: FilterDropdownProps) {
	const [isOpen, setIsOpen] = useState(false);
	const [selectedCategories, setSelectedCategories] = useState<string[]>(
		currentFilters.categories
	);
	const [selectedSubcategories, setSelectedSubcategories] = useState<string[]>(
		currentFilters.subcategories
	);
	const dropdownRef = useRef<HTMLDivElement>(null);

	const categories = getPOICategories();
	const categoryColors = getAllCategoryColors();

	// Close dropdown when clicking outside
	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (
				dropdownRef.current &&
				!dropdownRef.current.contains(event.target as Node)
			) {
				setIsOpen(false);
			}
		};

		document.addEventListener('mousedown', handleClickOutside);
		return () => {
			document.removeEventListener('mousedown', handleClickOutside);
		};
	}, []);

	// Update local state when props change
	useEffect(() => {
		setSelectedCategories(currentFilters.categories);
		setSelectedSubcategories(currentFilters.subcategories);
	}, [currentFilters]);

	const handleCategoryToggle = useCallback((category: string) => {
		setSelectedCategories((prev) => {
			const newCategories = prev.includes(category)
				? prev.filter((c) => c !== category)
				: [...prev, category];

			// If category is deselected, remove its subcategories
			if (!newCategories.includes(category)) {
				const categorySubcategories = getPOISubcategories(category);
				setSelectedSubcategories((prevSub) =>
					prevSub.filter((sub) => !categorySubcategories.includes(sub))
				);
			}

			return newCategories;
		});
	}, []);

	const handleSubcategoryToggle = useCallback((subcategory: string) => {
		setSelectedSubcategories((prev) =>
			prev.includes(subcategory)
				? prev.filter((s) => s !== subcategory)
				: [...prev, subcategory]
		);
	}, []);

	const applyFilters = useCallback(() => {
		console.log('Applying filters:', {
			categories: selectedCategories,
			subcategories: selectedSubcategories,
		});
		onFilterChange({
			categories: selectedCategories,
			subcategories: selectedSubcategories,
		});
		setIsOpen(false);
	}, [selectedCategories, selectedSubcategories, onFilterChange]);

	const clearFilters = useCallback(() => {
		setSelectedCategories([]);
		setSelectedSubcategories([]);
		onFilterChange({
			categories: [],
			subcategories: [],
		});
	}, [onFilterChange]);

	const hasActiveFilters =
		currentFilters.categories.length > 0 ||
		currentFilters.subcategories.length > 0;

	const getCategoryColor = (category: string) => {
		const categoryColor = categoryColors.find((c) => c.category === category);
		return categoryColor?.color || colors.ui['gray-5'];
	};

	return (
		<div
			className='relative'
			ref={dropdownRef}>
			{/* Filter Button */}
			<button
				onClick={() => setIsOpen(!isOpen)}
				className='px-4 py-3 font-medium transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105 flex items-center gap-2'
				style={{
					background: hasActiveFilters
						? `linear-gradient(135deg, ${colors.brand.primary} 0%, ${colors.brand.accent} 100%)`
						: `linear-gradient(135deg, ${colors.ui['gray-5']} 0%, ${colors.ui['gray-6']} 100%)`,
					color: 'white',
					border: 'none',
				}}
				title='Filter POIs by category'>
				<FaFilter className='w-4 h-4' />
				<span className='text-sm font-medium hidden sm:inline'>
					Filter
					{hasActiveFilters &&
						` (${
							currentFilters.categories.length +
							currentFilters.subcategories.length
						})`}
				</span>
			</button>

			{/* Dropdown Panel */}
			{isOpen && (
				<div
					className='absolute top-full right-0 mt-2 w-80 z-50 shadow-xl border'
					style={{
						background: 'rgba(255, 255, 255, 0.95)',
						backdropFilter: 'blur(8px)',
						borderColor: colors.ui.gray200,
					}}>
					{/* Header */}
					<div
						className='px-4 py-3 border-b flex items-center justify-between'
						style={{ borderColor: colors.ui.gray200 }}>
						<h3
							className='font-semibold'
							style={{ color: colors.neutral.textBlack }}>
							Filter POIs
						</h3>
						<button
							onClick={() => setIsOpen(false)}
							className='p-1 hover:bg-gray-100 transition-colors duration-200'
							style={{ color: colors.ui.gray500 }}>
							<FaTimes className='w-4 h-4' />
						</button>
					</div>

					{/* Categories */}
					<div className='p-4 max-h-96 overflow-y-auto'>
						<div className='mb-4'>
							<h4
								className='text-sm font-medium mb-2'
								style={{ color: colors.neutral.slateGray }}>
								Categories
							</h4>
							<div className='space-y-2'>
								{categories.map((category) => (
									<label
										key={category}
										className='flex items-center gap-3 cursor-pointer hover:bg-gray-50 p-2 transition-colors duration-200'>
										<input
											type='checkbox'
											checked={selectedCategories.includes(category)}
											onChange={() => handleCategoryToggle(category)}
											className='w-4 h-4'
											style={{ accentColor: getCategoryColor(category) }}
										/>
										<div
											className='w-4 h-4 border-2'
											style={{
												backgroundColor: getCategoryColor(category),
												borderColor: getCategoryColor(category),
											}}
										/>
										<span
											className='text-sm'
											style={{ color: colors.neutral.textBlack }}>
											{category}
										</span>
									</label>
								))}
							</div>
						</div>

						{/* Subcategories for selected categories */}
						{selectedCategories.length > 0 && (
							<div className='mb-4'>
								<h4
									className='text-sm font-medium mb-2'
									style={{ color: colors.neutral.slateGray }}>
									Subcategories
								</h4>
								<div className='space-y-2'>
									{selectedCategories.map((category) => {
										const subcategories = getPOISubcategories(category);
										return subcategories.map((subcategory) => (
											<label
												key={subcategory}
												className='flex items-center gap-3 cursor-pointer hover:bg-gray-50 p-2 transition-colors duration-200 ml-4'>
												<input
													type='checkbox'
													checked={selectedSubcategories.includes(subcategory)}
													onChange={() => handleSubcategoryToggle(subcategory)}
													className='w-4 h-4'
													style={{ accentColor: getCategoryColor(category) }}
												/>
												<span
													className='text-sm'
													style={{ color: colors.neutral.textBlack }}>
													{subcategory}
												</span>
											</label>
										));
									})}
								</div>
							</div>
						)}
					</div>

					{/* Actions */}
					<div
						className='px-4 py-3 border-t flex gap-2'
						style={{ borderColor: colors.ui.gray200 }}>
						<button
							onClick={clearFilters}
							className='px-3 py-2 text-sm font-medium transition-all duration-200 flex-1'
							style={{
								background: colors.ui.gray100,
								color: colors.neutral.slateGray,
								border: `1px solid ${colors.ui.gray200}`,
							}}>
							Clear All
						</button>
						<button
							onClick={applyFilters}
							className='px-3 py-2 text-sm font-medium transition-all duration-200 flex-1'
							style={{
								background: `linear-gradient(135deg, ${colors.brand.primary} 0%, ${colors.brand.accent} 100%)`,
								color: 'white',
								border: 'none',
							}}>
							Apply Filters
						</button>
					</div>
				</div>
			)}
		</div>
	);
}
