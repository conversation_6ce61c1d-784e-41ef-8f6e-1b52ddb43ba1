/** @format */

import { colors } from '@/app/colors';
import { POI_CATEGORIES_DATA } from '@/app/shared/poi/constants';

// Category border colors - each category gets a distinct border color
export const CATEGORY_BORDER_COLORS: Record<string, string> = {
	'Food & Drink': colors.supporting['sup-color1'], // Vibrant Coral Red
	'Cultural & Creative Experiences': colors.supporting['sup-color6'], // Soft Purple
	'Sports & Fitness': colors.brand.green, // Vibrant Green
	Entertainment: colors.supporting['sup-color3'], // Electric Blue
	'Shopping & Markets': colors.supporting['sup-color5'], // Warm Yellow
	'Outdoor & Nature': colors.brand.accent, // Nature Green
	'Wellness & Beauty': colors.supporting['sup-color4'], // Mint Green
	Transportation: colors.brand.primary, // Modern Sky Blue
};

// Subcategory fill colors - based on importance level
export const getSubcategoryFillColor = (
	subcategory: string,
	category: string
): string => {
	// Find the subcategory in the category data
	const categoryData =
		POI_CATEGORIES_DATA[category as keyof typeof POI_CATEGORIES_DATA];

	if (!categoryData) {
		return colors.ui['gray-5']; // Default gray
	}

	const subcategoryData = categoryData.subcategories.find(
		(sub) => sub.name === subcategory
	);

	if (!subcategoryData) {
		return colors.ui['gray-5']; // Default gray
	}

	// Map importance levels to color intensities
	// Higher importance = more vibrant/saturated colors
	const importance = subcategoryData.importance;

	if (importance >= 9) {
		return colors.utility.success; // High importance - vibrant green
	} else if (importance >= 7) {
		return colors.brand.primary; // Medium-high importance - sky blue
	} else if (importance >= 5) {
		return colors.supporting['sup-color2']; // Medium importance - turquoise
	} else if (importance >= 3) {
		return colors.supporting['sup-color4']; // Low-medium importance - mint
	} else {
		return colors.ui.gray500; // Low importance - gray
	}
};

// Get border color for a category
export const getCategoryBorderColor = (category: string): string => {
	return CATEGORY_BORDER_COLORS[category] || colors.ui.gray500; // Default dark gray
};

// Create marker style for POI
export const getPOIMarkerStyle = (
	category: string,
	subcategory?: string
): {
	borderColor: string;
	fillColor: string;
	borderWidth: number;
	radius: number;
} => {
	const borderColor = getCategoryBorderColor(category);
	const fillColor = subcategory
		? getSubcategoryFillColor(subcategory, category)
		: colors.ui.gray400; // Default fill if no subcategory

	return {
		borderColor,
		fillColor,
		borderWidth: 3, // Thicker border to make category color more visible
		radius: 10, // Slightly larger for better visibility
	};
};

// Helper function to get all category colors for legend/filter UI
export const getAllCategoryColors = (): Array<{
	category: string;
	color: string;
}> => {
	return Object.entries(CATEGORY_BORDER_COLORS).map(([category, color]) => ({
		category,
		color,
	}));
};

// Helper function to get subcategory colors for a specific category
export const getSubcategoryColors = (
	category: string
): Array<{ subcategory: string; color: string; importance: number }> => {
	const categoryData =
		POI_CATEGORIES_DATA[category as keyof typeof POI_CATEGORIES_DATA];

	if (!categoryData) {
		return [];
	}

	return categoryData.subcategories.map((sub) => ({
		subcategory: sub.name,
		color: getSubcategoryFillColor(sub.name, category),
		importance: sub.importance,
	}));
};
