/** @format */

import {
	ChatState,
	ChatUIState,
	ExtractedLocation,
	Message,
	Session,
	TopCandidates,
	UseChatActionsProps,
	UseChatMessagesProps,
	UseChatSessionProps,
	UseChatSessionsProps,
	UseMessageActionsProps,
} from 'app/chat/types';
import {
	useLocationManager,
	useLocationSetup,
} from 'app/shared/locationManager';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useCallback, useEffect, useRef, useState } from 'react';

// ===== CHAT STATE HOOK =====

export function useChatState() {
	const [chatState, setChatState] = useState<ChatState>({
		messages: [],
		newMessage: '',
		sessionId: null,
		sessionList: [],
		topCandidates: null,
		allExtractedLocations: [],
		isSending: false,
	});

	// Individual setters for convenience (memoized to prevent infinite loops)
	const setMessages = useCallback(
		(messages: Message[] | ((prev: Message[]) => Message[])) => {
			setChatState((prev) => ({
				...prev,
				messages:
					typeof messages === 'function' ? messages(prev.messages) : messages,
			}));
		},
		[]
	);

	const setNewMessage = useCallback((newMessage: string) => {
		setChatState((prev) => ({ ...prev, newMessage }));
	}, []);

	const setSessionId = useCallback((sessionId: string | null) => {
		setChatState((prev) => ({ ...prev, sessionId }));
	}, []);

	const setSessionList = useCallback(
		(sessionList: Session[] | ((prev: Session[]) => Session[])) => {
			setChatState((prev) => ({
				...prev,
				sessionList:
					typeof sessionList === 'function'
						? sessionList(prev.sessionList)
						: sessionList,
			}));
		},
		[]
	);

	const setTopCandidates = useCallback(
		(topCandidates: TopCandidates | null) => {
			setChatState((prev) => ({ ...prev, topCandidates }));
		},
		[]
	);

	const setAllExtractedLocations = useCallback(
		(
			locations:
				| Array<Record<string, unknown>>
				| ((
						prev: Array<Record<string, unknown>>
				  ) => Array<Record<string, unknown>>)
		) => {
			setChatState((prev) => ({
				...prev,
				allExtractedLocations:
					typeof locations === 'function'
						? (locations(prev.allExtractedLocations) as ExtractedLocation[])
						: (locations as ExtractedLocation[]),
			}));
		},
		[]
	);

	const addExtractedLocations = useCallback(
		(newLocations: Array<Record<string, unknown>>, messageIndex: number) => {
			if (!newLocations || newLocations.length === 0) return;

			const locationsWithIndex = newLocations.map((loc) => ({
				...loc,
				messageIndex,
			})) as ExtractedLocation[];

			setChatState((prev) => ({
				...prev,
				allExtractedLocations: [
					...prev.allExtractedLocations,
					...locationsWithIndex,
				],
			}));
		},
		[]
	);

	const setIsSending = useCallback((isSending: boolean) => {
		setChatState((prev) => ({ ...prev, isSending }));
	}, []);

	return {
		chatState,
		setChatState,
		// Individual setters
		setMessages,
		setNewMessage,
		setSessionId,
		setSessionList,
		setTopCandidates,
		setAllExtractedLocations,
		addExtractedLocations,
		setIsSending,
	};
}

// ===== CHAT UI HOOK =====

export function useChatUI() {
	const [uiState, setUIState] = useState<ChatUIState>({
		isLeftOpen: false,
		isRightOpen: false,
		dropdownIndex: null,
		showScrollButton: false,
		isClient: false,
		isGlobeTransitioning: false,
	});

	// Initialize client-side rendering
	useEffect(() => {
		setUIState((prev) => ({ ...prev, isClient: true }));
	}, []);

	// Individual setters for convenience (memoized to prevent infinite loops)
	const setIsLeftOpen = useCallback((isLeftOpen: boolean) => {
		setUIState((prev) => ({ ...prev, isLeftOpen }));
	}, []);

	const setIsRightOpen = useCallback((isRightOpen: boolean) => {
		setUIState((prev) => ({ ...prev, isRightOpen }));
	}, []);

	const setDropdownIndex = useCallback((dropdownIndex: number | null) => {
		setUIState((prev) => ({ ...prev, dropdownIndex }));
	}, []);

	const setShowScrollButton = useCallback((showScrollButton: boolean) => {
		setUIState((prev) => ({ ...prev, showScrollButton }));
	}, []);

	const setIsGlobeTransitioning = useCallback(
		(isGlobeTransitioning: boolean) => {
			setUIState((prev) => ({ ...prev, isGlobeTransitioning }));
		},
		[]
	);

	const toggleDropdown = useCallback((index: number) => {
		setUIState((prev) => ({
			...prev,
			dropdownIndex: prev.dropdownIndex === index ? null : index,
		}));
	}, []);

	const handleGlobeTransition = useCallback(() => {
		setUIState((prev) => ({ ...prev, isGlobeTransitioning: true }));
		setTimeout(() => {
			window.location.href = '/globe';
		}, 1000);
	}, []);

	return {
		uiState,
		setUIState,
		// Individual setters
		setIsLeftOpen,
		setIsRightOpen,
		setDropdownIndex,
		setShowScrollButton,
		setIsGlobeTransitioning,
		// Actions
		toggleDropdown,
		handleGlobeTransition,
	};
}

// ===== CHAT MESSAGES HOOK =====

export function useChatMessages({
	messages,
	isSending,
	setShowScrollButton,
}: UseChatMessagesProps) {
	const inputRef = useRef<HTMLTextAreaElement>(null!);
	const messagesEndRef = useRef<HTMLDivElement>(null!);
	const messagesContainerRef = useRef<HTMLDivElement>(null!);

	// Auto-scroll to bottom when new messages arrive
	useEffect(() => {
		messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
	}, [messages]);

	// Focus input when not sending
	useEffect(() => {
		if (!isSending) inputRef.current?.focus();
	}, [messages, isSending]);

	// Handle scroll button visibility
	useEffect(() => {
		const container = messagesContainerRef.current;
		if (!container) return;

		const handleScroll = () => {
			const isAtBottom =
				container.scrollHeight - container.scrollTop <=
				container.clientHeight + 20;
			setShowScrollButton(!isAtBottom);
		};

		container.addEventListener('scroll', handleScroll);
		return () => container.removeEventListener('scroll', handleScroll);
	}, [setShowScrollButton]);

	const scrollToBottom = useCallback(() => {
		messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
		setShowScrollButton(false);
	}, [setShowScrollButton]);

	const handleInputChange = useCallback(() => {
		if (inputRef.current) {
			inputRef.current.style.height = 'auto';
			inputRef.current.style.height = `${Math.min(
				inputRef.current.scrollHeight,
				120
			)}px`;
		}
	}, []);

	return {
		inputRef,
		messagesEndRef,
		messagesContainerRef,
		scrollToBottom,
		handleInputChange,
	};
}

// ===== CHAT SESSIONS HOOK =====

export function useChatSessions({
	sessionList,
	sessionId,
	isClient,
	setSessionList,
}: UseChatSessionsProps) {
	const { data: session } = useSession();
	const hasLoadedRef = useRef(false);

	// Load session titles when component mounts (only once)
	useEffect(() => {
		const loadSessionTitles = async () => {
			if (!session?.user?.id || hasLoadedRef.current) return;

			hasLoadedRef.current = true;

			try {
				const response = await fetch('/api/chat/titles', {
					method: 'POST',
					headers: { 'Content-Type': 'application/json' },
					body: JSON.stringify({ userId: session.user.id }),
				});

				if (response.ok) {
					const data = await response.json();
					if (data.titles && Array.isArray(data.titles)) {
						const sessions = data.titles.map(
							(title: { session_id: string; title: string }) => ({
								id: title.session_id,
								title: title.title || 'Untitled Session',
							})
						);
						setSessionList(sessions);
					}
				}
			} catch (error) {
				console.error('Failed to load session titles:', error);
				hasLoadedRef.current = false; // Reset on error to allow retry
			}
		};

		if (isClient && session?.user?.id) {
			loadSessionTitles();
		}
	}, [isClient, session?.user?.id, setSessionList]);

	// Set page title based on session
	useEffect(() => {
		const sessionTitle =
			sessionList.find((s) => s.id === sessionId)?.title || '';
		document.title = sessionTitle ? `Wizlop | ${sessionTitle}` : 'Wizlop';
	}, [sessionList, sessionId]);

	return {
		// Could add session-specific actions here if needed
	};
}

// ===== LOCATION HANDLER HOOK =====

// Type declaration for window object
declare global {
	interface Window {
		locationPermissionResolve?: (result: 'success' | 'cancelled') => void;
	}
}

export function useLocationHandler() {
	const router = useRouter();
	const { triggerLocationSetupIfNeeded, handleLocationSetupComplete } =
		useLocationSetup();
	const {
		location: userLocation,
		setupStatus,
		requestAutoLocation,
		needsLocationSetup: originalNeedsLocationSetup,
		markSetupComplete: originalMarkSetupComplete,
		isInitialized,
		isLoading,
		error,
	} = useLocationManager();

	const handleLocationPermissionFlow = useCallback(async (): Promise<
		'success' | 'cancelled'
	> => {
		// If we already have a location, proceed
		if (userLocation) {
			return 'success';
		}

		// Check if user has completed setup but chose 'none' - allow proceeding without location
		if (setupStatus.hasSetup && setupStatus.setupChoice === 'none') {
			return 'success';
		}

		// Trigger location setup if needed
		const setupTriggered = triggerLocationSetupIfNeeded();

		if (setupTriggered) {
			// Wait for setup to complete
			return new Promise((resolve) => {
				window.locationPermissionResolve = resolve;
			});
		}

		// If setup wasn't triggered but we don't have location, check the setup choice
		if (setupStatus.hasSetup) {
			if (setupStatus.setupChoice === 'none') {
				return 'success'; // User chose to proceed without location
			} else if (setupStatus.setupChoice === 'manual') {
				// User chose manual but hasn't set location yet - trigger setup again
				const retrigger = triggerLocationSetupIfNeeded();
				if (retrigger) {
					return new Promise((resolve) => {
						window.locationPermissionResolve = resolve;
					});
				}
			}
		}

		// If setup wasn't triggered (already completed), proceed
		return 'success';
	}, [userLocation, setupStatus, triggerLocationSetupIfNeeded]);

	const handleLocationSetupCompleteInternal = useCallback(
		(choice: 'auto' | 'manual' | 'none') => {
			console.log('Location setup completed with choice:', choice);
			handleLocationSetupComplete(choice);

			// Handle navigation based on choice
			if (choice === 'manual') {
				// Redirect to settings for manual location entry
				router.push(
					'/settings?tab=location&return=' + encodeURIComponent('/chat')
				);
			}
			// For 'auto' and 'none', stay on the current page (chat)

			// Always resolve the promise immediately since LocationSetup handles the flow
			console.log('Resolving location setup');
			if (window.locationPermissionResolve) {
				window.locationPermissionResolve('success');
				delete window.locationPermissionResolve;
			}
		},
		[handleLocationSetupComplete, router]
	);

	// Use the correct needsLocationSetup from useLocationManager
	const needsLocationSetup = useCallback(() => {
		return originalNeedsLocationSetup();
	}, [originalNeedsLocationSetup]);

	const locationState = {
		userLocation,
		locationError: error,
		locationLoading: isLoading,
		requestAutoLocation,
		markSetupComplete: originalMarkSetupComplete,
		needsLocationSetup,
		isInitialized,
	};

	return {
		locationState,
		handleLocationPermissionFlow,
		handleLocationSetupComplete: handleLocationSetupCompleteInternal,
		userLocation,
	};
}

// ===== CHAT ACTIONS HOOK =====

export function useChatActions({
	sessionId,
	setSessionId,
	setMessages,
	setSessionList,
	setTopCandidates,
	setAllExtractedLocations,
	addExtractedLocations,
	setIsSending,
	setNewMessage,
	setIsRightOpen,
	inputRef,
	userLocation,
}: UseChatActionsProps) {
	const { data: session } = useSession();

	const createSessionIfNeeded = useCallback(async (): Promise<
		string | null
	> => {
		console.log(
			'🔍 createSessionIfNeeded called, current sessionId:',
			sessionId
		);
		if (sessionId) {
			console.log('✅ Session already exists:', sessionId);
			return sessionId;
		}

		try {
			console.log('🔄 Creating new session for user:', session?.user?.id);
			const res = await fetch('/api/chat/session', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({ userId: session?.user?.id }),
			});

			if (!res.ok) {
				let errorMessage = 'Session could not be created.';
				try {
					const errorData = await res.json();
					errorMessage =
						errorData.error?.message || errorData.error || errorMessage;
				} catch {
					errorMessage = `${errorMessage} (${res.status}: ${res.statusText})`;
				}
				throw new Error(errorMessage);
			}

			const data = await res.json();
			const newId = data.sessionId;
			console.log('✅ Session created successfully:', newId);
			setSessionId(newId);

			setSessionList((prev) => {
				const sessionExists = prev.some((session) => session.id === newId);
				if (!sessionExists) {
					console.log('📝 Adding new session to list:', newId);
					return [{ id: newId, title: '' }, ...prev];
				}
				console.log('⚠️ Session already exists in list:', newId);
				return prev;
			});

			return newId;
		} catch (err) {
			console.error('❌ Session creation failed', err);
			return null;
		}
	}, [sessionId, session?.user?.id, setSessionId, setSessionList]);

	const sendMessage = useCallback(
		async (message: string) => {
			console.log('🚀 sendMessage called with:', {
				message,
				sessionId,
				userId: session?.user?.id,
			});

			if (!message.trim() || !session?.user?.id) {
				console.log('❌ Message validation failed:', {
					messageEmpty: !message.trim(),
					noUserId: !session?.user?.id,
				});
				return;
			}

			const userMessage: Message = { text: message, isUser: true };
			const loadingMessage: Message = {
				text: '',
				isUser: false,
				isLoading: true,
			};

			setMessages((prev) => [...prev, userMessage, loadingMessage]);
			setNewMessage('');
			setIsSending(true);

			try {
				// Create session if needed and get the current session ID
				const currentSessionId = await createSessionIfNeeded();
				console.log(
					'🎯 Got currentSessionId from createSessionIfNeeded:',
					currentSessionId
				);

				if (!currentSessionId) {
					console.error('❌ No session ID available, cannot send message');
					setMessages((prev) => {
						const updated = [...prev];
						updated[updated.length - 1] = {
							text: '❌ Failed to create session.',
							isUser: false,
							isLoading: false,
						};
						return updated;
					});
					return;
				}

				console.log('📍 User location data:', userLocation);

				const payload = {
					message,
					sessionId: currentSessionId,
					userId: session.user.id,
					userLat: userLocation?.lat || null,
					userLng: userLocation?.lng || null,
				};

				console.log(
					'📦 Final request payload:',
					JSON.stringify(payload, null, 2)
				);

				// Create AbortController with 2.5 minute timeout
				const controller = new AbortController();
				const timeoutId = setTimeout(() => {
					controller.abort();
				}, 150000); // 2.5 minutes = 150,000 milliseconds

				try {
					const response = await fetch('/api/chat/message', {
						method: 'POST',
						headers: { 'Content-Type': 'application/json' },
						body: JSON.stringify(payload),
						signal: controller.signal,
					});

					// Clear timeout once response starts (headers received)
					clearTimeout(timeoutId);

					if (!response.ok) {
						let errorMessage = 'Message could not be sent.';
						try {
							const errorData = await response.json();

							// Handle credit-specific errors
							if (
								response.status === 402 &&
								errorData.code === 'INSUFFICIENT_CREDITS'
							) {
								errorMessage = `💳 ${errorData.error} Visit the Credits page to purchase more credits or earn them by contributing to the platform.`;
							} else {
								errorMessage =
									errorData.error?.message || errorData.error || errorMessage;
							}
						} catch {
							errorMessage = `${errorMessage} (${response.status}: ${response.statusText})`;
						}
						throw new Error(errorMessage);
					}

					// Handle Server-Sent Events (SSE) streaming response
					const reader = response.body?.getReader();
					const decoder = new TextDecoder();
					let buffer = '';
					let fullText = '';
					let sessionTitle = '';
					let topCandidates = null;
					let assistantMessageIndex = 0;

					// Initialize the assistant message
					setMessages((prev) => {
						const newMessages = [...prev];
						assistantMessageIndex = newMessages.length - 1;
						return newMessages;
					});

					if (reader) {
						try {
							while (true) {
								const { done, value } = await reader.read();
								if (done) break;

								buffer += decoder.decode(value, { stream: true });
								const lines = buffer.split('\n');
								buffer = lines.pop() || ''; // Keep incomplete line in buffer

								for (const line of lines) {
									if (line.startsWith('data: ')) {
										try {
											const jsonStr = line.slice(6); // Remove 'data: ' prefix
											const eventData = JSON.parse(jsonStr);
											console.log('📦 SSE Event:', eventData);

											if (eventData.type === 'llm_response' && eventData.data) {
												const { data: responseData } = eventData;

												// Handle text chunks
												if (responseData.type === 'text_chunk') {
													fullText += responseData.content;
													// Update message in real-time
													setMessages((prev) => {
														const newMessages = [...prev];
														newMessages[assistantMessageIndex] = {
															text: fullText,
															isUser: false,
															isLoading: !responseData.is_final_text_chunk,
														};
														return newMessages;
													});
												}
												// Handle candidates
												else if (responseData.type === 'candidates') {
													topCandidates = responseData.top_candidates || null;
													console.log('📍 Candidates received:', topCandidates);
												}
												// Handle session title
												else if (responseData.type === 'session_title') {
													sessionTitle = responseData.session_title;
													console.log(
														'🏷️ Session title received:',
														sessionTitle
													);
												}
											}
										} catch (parseError) {
											console.warn(
												'Failed to parse SSE data:',
												line,
												parseError
											);
										}
									}
								}
							}
						} finally {
							reader.releaseLock();
						}
					}

					console.log('📥 Streaming complete:', {
						fullText,
						sessionTitle,
						topCandidates,
					});

					// Final message update
					setMessages((prev) => {
						const newMessages = [...prev];
						newMessages[assistantMessageIndex] = {
							text: fullText || 'No response from server.',
							isUser: false,
							isLoading: false,
						};
						return newMessages;
					});

					// Set top candidates
					setTopCandidates(topCandidates);

					// Add new extracted locations to the accumulated list
					if (
						topCandidates &&
						Array.isArray(topCandidates) &&
						topCandidates.length > 0
					) {
						addExtractedLocations(topCandidates, assistantMessageIndex);
						setIsRightOpen(true);
					}

					// Update session title if provided
					console.log('🏷️ Final session title:', sessionTitle);
					if (sessionTitle && sessionTitle.trim()) {
						console.log('📝 Updating session title:', sessionTitle);
						setSessionList((prev) => {
							const sessionExists = prev.some(
								(session) => session.id === currentSessionId
							);
							console.log(
								'🔍 Session exists in list:',
								sessionExists,
								'Current session ID:',
								currentSessionId
							);

							if (sessionExists) {
								const updated = prev.map((session) =>
									session.id === currentSessionId
										? { ...session, title: sessionTitle }
										: session
								);
								console.log('✅ Updated session list with title:', updated);
								return updated;
							} else {
								const newList = [
									{ id: currentSessionId, title: sessionTitle },
									...prev,
								];
								console.log('➕ Added new session with title:', newList);
								return newList;
							}
						});
					} else {
						console.log(
							'⚠️ No session title provided in response or title is empty'
						);
						// Ensure session exists in list even without a title
						setSessionList((prev) => {
							const sessionExists = prev.some(
								(session) => session.id === currentSessionId
							);
							if (!sessionExists) {
								const fallbackTitle = `Chat ${new Date().toLocaleString()}`;
								console.log(
									'🔄 Creating session with fallback title:',
									fallbackTitle
								);
								return [
									{ id: currentSessionId, title: fallbackTitle },
									...prev,
								];
							}
							return prev;
						});
					}
				} catch (fetchError) {
					// Clear timeout in case of error
					clearTimeout(timeoutId);
					throw fetchError;
				}
			} catch (error) {
				console.error('❌ Error during message sending:', error);

				// Handle specific abort error with custom message
				let errorMessage = 'Failed to send message.';
				if (error instanceof Error) {
					if (error.name === 'AbortError') {
						errorMessage =
							'Request timed out after 2.5 minutes. The LLM engine is taking longer than expected to respond.';
					} else {
						errorMessage = error.message;
					}
				}

				setMessages((prev) => {
					const newMessages = [...prev];
					newMessages[newMessages.length - 1] = {
						text: `❌ Error: ${errorMessage}`,
						isUser: false,
						isLoading: false,
					};
					return newMessages;
				});
			} finally {
				setIsSending(false);
				inputRef.current?.focus();
			}
		},
		[
			createSessionIfNeeded,
			session?.user?.id,
			userLocation,
			setMessages,
			setNewMessage,
			setIsSending,
			setSessionList,
			setTopCandidates,
			setIsRightOpen,
			inputRef,
		]
	);

	const startNewChat = useCallback(async () => {
		setMessages([]);
		setSessionId(null);
		setTopCandidates(null);
		setAllExtractedLocations([]);
		setNewMessage('');
		inputRef.current?.focus();
	}, [
		setMessages,
		setSessionId,
		setTopCandidates,
		setAllExtractedLocations,
		setNewMessage,
		inputRef,
	]);

	const loadOldMessages = useCallback(
		async (selectedSessionId: string) => {
			if (!session?.user?.id || selectedSessionId === sessionId) return;

			console.log('🔄 Loading old messages for session:', selectedSessionId);
			setSessionId(selectedSessionId);
			setMessages([]);
			setTopCandidates(null);
			setAllExtractedLocations([]);

			try {
				const res = await fetch('/api/chat/messages', {
					method: 'POST',
					headers: { 'Content-Type': 'application/json' },
					body: JSON.stringify({
						userId: session.user.id,
						sessionId: selectedSessionId,
					}),
				});
				if (!res.ok) {
					throw new Error('Failed to load messages');
				}

				const data = await res.json();

				const loadedMessages = data.messages.map(
					(msg: { content: string; role: string }) => ({
						text: msg.content,
						isUser: msg.role === 'user',
						isLoading: false,
					})
				);

				setMessages(loadedMessages);

				// Extract ALL POIs from ALL assistant messages in the conversation
				const allExtractedPOIs: Array<Record<string, unknown>> = [];
				data.messages.forEach(
					(
						msg: {
							role: string;
							metadata?: { top_candidates?: Array<Record<string, unknown>> };
						},
						index: number
					) => {
						if (msg.role === 'assistant' && msg.metadata?.top_candidates) {
							const poisWithIndex = msg.metadata.top_candidates.map(
								(poi: Record<string, unknown>) => ({
									...poi,
									messageIndex: index,
								})
							);
							allExtractedPOIs.push(...poisWithIndex);
						}
					}
				);

				console.log(
					'🗺️ Loaded',
					allExtractedPOIs.length,
					'historical locations for session'
				);

				// Set the accumulated POIs
				setAllExtractedLocations(allExtractedPOIs);

				// Set the most recent topCandidates for compatibility
				const lastAssistantMessage = [...data.messages]
					.reverse()
					.find(
						(msg: {
							role: string;
							metadata?: { top_candidates?: Array<Record<string, unknown>> };
						}) => msg.role === 'assistant' && msg.metadata?.top_candidates
					);

				if (lastAssistantMessage?.metadata?.top_candidates) {
					setTopCandidates(lastAssistantMessage.metadata.top_candidates);
				} else {
					setTopCandidates(null);
				}

				// Open right sidebar if there are any extracted locations
				if (allExtractedPOIs.length > 0) {
					console.log('🗺️ Opening right sidebar to show historical locations');
					setIsRightOpen(true);
				}
			} catch (error) {
				console.error('Error loading messages:', error);
			}
		},
		[session?.user?.id, sessionId, setSessionId, setMessages, setTopCandidates]
	);

	const handleDeleteSession = useCallback(
		async (sessionIdToDelete: string) => {
			if (!session?.user?.id) return;

			try {
				const response = await fetch('/api/chat/delete-session', {
					method: 'POST',
					headers: { 'Content-Type': 'application/json' },
					body: JSON.stringify({
						userId: session.user.id,
						sessionId: sessionIdToDelete,
					}),
				});

				if (response.ok) {
					setSessionList((prev) =>
						prev.filter((session) => session.id !== sessionIdToDelete)
					);

					if (sessionId === sessionIdToDelete) {
						setMessages([]);
						setSessionId(null);
						setTopCandidates(null);
					}
				}
			} catch (error) {
				console.error('Error deleting session:', error);
			}
		},
		[
			session?.user?.id,
			sessionId,
			setSessionList,
			setMessages,
			setSessionId,
			setTopCandidates,
		]
	);

	return {
		sendMessage,
		startNewChat,
		loadOldMessages,
		handleDeleteSession,
	};
}

// ===== COMPONENT UTILITY HOOKS =====

export function useChatSession({
	sessionId,
	activeSessionId,
	onLoadOldMessages,
	onDeleteSession,
}: UseChatSessionProps) {
	const isActive = sessionId === activeSessionId;

	const handleSessionClick = useCallback(() => {
		if (sessionId !== activeSessionId) {
			onLoadOldMessages(sessionId);
		}
	}, [sessionId, activeSessionId, onLoadOldMessages]);

	const handleDeleteSession = useCallback(() => {
		onDeleteSession(sessionId);
	}, [sessionId, onDeleteSession]);

	return {
		isActive,
		handleSessionClick,
		handleDeleteSession,
	};
}

export function useMessageActions({ onShowMap }: UseMessageActionsProps) {
	const handleCopyMessage = useCallback((messageText: string) => {
		navigator.clipboard.writeText(messageText);
	}, []);

	const handleShowMap = useCallback(() => {
		onShowMap();
	}, [onShowMap]);

	return {
		handleCopyMessage,
		handleShowMap,
	};
}

// ===== UI UTILITY HOOKS =====

export function useModalState(initialState = false) {
	const [isOpen, setIsOpen] = useState(initialState);

	const openModal = useCallback(() => {
		setIsOpen(true);
	}, []);

	const closeModal = useCallback(() => {
		setIsOpen(false);
	}, []);

	const toggleModal = useCallback(() => {
		setIsOpen((prev) => !prev);
	}, []);

	return {
		isOpen,
		openModal,
		closeModal,
		toggleModal,
		setIsOpen,
	};
}

interface HoverEffectConfig {
	defaultColor: string;
	hoverColor: string;
	defaultBg?: string;
	hoverBg?: string;
}

export function useHoverEffects(config: HoverEffectConfig) {
	const handleMouseEnter = useCallback(
		(e: React.MouseEvent<HTMLElement>) => {
			e.currentTarget.style.color = config.hoverColor;
			if (config.hoverBg) {
				e.currentTarget.style.backgroundColor = config.hoverBg;
			}
		},
		[config.hoverColor, config.hoverBg]
	);

	const handleMouseLeave = useCallback(
		(e: React.MouseEvent<HTMLElement>) => {
			e.currentTarget.style.color = config.defaultColor;
			if (config.defaultBg !== undefined) {
				e.currentTarget.style.backgroundColor = config.defaultBg;
			}
		},
		[config.defaultColor, config.defaultBg]
	);

	return {
		handleMouseEnter,
		handleMouseLeave,
	};
}
