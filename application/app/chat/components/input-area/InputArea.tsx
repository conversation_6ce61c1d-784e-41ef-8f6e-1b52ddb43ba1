/** @format */

import { zIndexLayers } from '@/app/chat/styles';
import { BottomBarProps } from '@/app/chat/types';
import { colors } from '@/app/colors';
import React, { useState } from 'react';
import { CreditStatus } from '../credit-status';

const InputArea: React.FC<BottomBarProps> = ({
	newMessage,
	handleInputChange,
	handleKeyDown,
	inputRef,
	isSending,
	hasMessages,
}) => {
	const [showMicPopup, setShowMicPopup] = useState(false);
	const isChatting = newMessage.length > 0 || isSending || hasMessages;

	const handleInputFocus = () => {
		// Don't change to chatting mode on focus, only on actual typing/enter
		// Close mic popup when focusing on input
		setShowMicPopup(false);
	};

	return (
		<div
			className={`transition-all duration-500 ease-out px-5 flex justify-center ${
				zIndexLayers.bottomBar
			} ${
				isChatting
					? 'items-end pb-5 h-auto min-h-[100px]'
					: 'items-center relative h-screen pb-0'
			}`}
			onClick={() => setShowMicPopup(false)}>
			<div
				className={`w-full max-w-[800px] flex flex-col items-center transition-all duration-500 ease-out ${
					isChatting ? 'translate-y-0' : 'translate-y-0'
				}`}>
				{!isChatting && (
					<div
						className='text-3xl font-bold mb-12 text-center transition-opacity duration-500'
						style={{ color: colors.neutral.textBlack }}>
						Where do you want to explore today?
					</div>
				)}

				{/* Input Area - Fixed positioning and layout */}
				<div className='w-full relative flex items-center'>
					<div className='w-full relative'>
						<textarea
							ref={inputRef}
							value={newMessage}
							onChange={(e) => {
								handleInputChange(e);
								// Auto-resize
								if (inputRef.current) {
									inputRef.current.style.height = 'auto';
									inputRef.current.style.height = `${Math.min(
										inputRef.current.scrollHeight,
										120
									)}px`;
								}
							}}
							onKeyDown={handleKeyDown}
							onFocus={(e) => {
								handleInputFocus();
								e.currentTarget.style.borderColor = colors.brand.blue;
								e.currentTarget.style.boxShadow = `0 0 0 2px ${colors.brand.blue}40`;
							}}
							onBlur={(e) => {
								e.currentTarget.style.borderColor = colors.ui.blue200;
								e.currentTarget.style.boxShadow = 'none';
							}}
							placeholder={
								isChatting
									? 'Type your message...'
									: 'Ask me about any place in the world...'
							}
							disabled={isSending}
							className='w-full p-4 pr-24 rounded-2xl border resize-none focus:outline-none transition-all hide-scrollbar'
							style={{
								backgroundColor: colors.neutral.cloudWhite,
								borderColor: colors.ui.blue200,
								color: colors.neutral.textBlack,
								minHeight: '56px',
								maxHeight: '120px',
								fontSize: '16px', // Prevent zoom on iOS
								lineHeight: '1.5',
								overflowY: 'auto',
								scrollbarWidth: 'none', // Firefox
							}}
							rows={1}
						/>
						{/* Hide scrollbar for Webkit browsers */}
						<style>{`
              .hide-scrollbar::-webkit-scrollbar {
                display: none;
              }
            `}</style>

						{/* Input Buttons Container - Perfectly Centered */}
						<div
							className='absolute right-3 flex items-center gap-3'
							style={{
								top: '50%',
								transform: 'translateY(-50%)',
								marginTop: '-2px', // Fine-tune vertical alignment
							}}>
							{/* Microphone Button - Shows popup on click */}
							<div className='relative'>
								<button
									onClick={(e) => {
										e.stopPropagation();
										setShowMicPopup(!showMicPopup);
									}}
									className='w-10 h-10 transition-all duration-300 flex items-center justify-center border'
									style={{
										backgroundColor: 'transparent',
										color: colors.neutral.slateGray,
										borderColor: colors.ui.gray300,
									}}
									onMouseEnter={(e) => {
										e.currentTarget.style.color = colors.brand.blue;
										e.currentTarget.style.borderColor = colors.ui.blue200;
									}}
									onMouseLeave={(e) => {
										e.currentTarget.style.color = colors.neutral.slateGray;
										e.currentTarget.style.borderColor = colors.ui.gray300;
									}}
									title='Voice input'>
									<svg
										width='18'
										height='18'
										viewBox='0 0 24 24'
										fill='none'
										stroke='currentColor'
										strokeWidth='2'
										strokeLinecap='round'
										strokeLinejoin='round'>
										<path d='M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z' />
										<path d='M19 10v2a7 7 0 0 1-14 0v-2' />
										<line
											x1='12'
											x2='12'
											y1='19'
											y2='22'
										/>
										<line
											x1='8'
											x2='16'
											y1='22'
											y2='22'
										/>
									</svg>
								</button>

								{/* Mic Not Supported Popup */}
								{showMicPopup && (
									<div
										className='absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 text-sm border shadow-lg z-50'
										style={{
											backgroundColor: colors.neutral.cloudWhite,
											borderColor: colors.ui.gray200,
											color: colors.neutral.textBlack,
											boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
											whiteSpace: 'nowrap',
										}}>
										Mic is not supported yet
										<div
											className='absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0'
											style={{
												borderLeft: '6px solid transparent',
												borderRight: '6px solid transparent',
												borderTop: `6px solid ${colors.ui.gray200}`,
											}}></div>
									</div>
								)}
							</div>

							{/* Send Button - Fully functional */}
							<button
								onClick={() => {
									console.log(
										'Send button clicked, newMessage:',
										newMessage,
										'isSending:',
										isSending
									);
									if (newMessage && newMessage.trim() && !isSending) {
										// Create a synthetic keyboard event to trigger the send
										const syntheticEvent = {
											key: 'Enter',
											shiftKey: false,
											preventDefault: () => {},
											stopPropagation: () => {},
										} as React.KeyboardEvent<HTMLTextAreaElement>;
										handleKeyDown(syntheticEvent);
									}
								}}
								disabled={!newMessage || !newMessage.trim() || isSending}
								className='w-10 h-10 transition-all duration-300 flex items-center justify-center shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed border'
								style={{
									background:
										newMessage && newMessage.trim() && !isSending
											? `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.supporting.lightBlue} 100%)`
											: `linear-gradient(135deg, ${colors.ui.gray300} 0%, ${colors.ui.gray400} 100%)`,
									color: 'white',
									borderColor:
										newMessage && newMessage.trim() && !isSending
											? colors.brand.blue
											: colors.ui.gray400,
								}}
								onMouseEnter={(e) => {
									if (newMessage && newMessage.trim() && !isSending) {
										e.currentTarget.style.background = `linear-gradient(135deg, ${colors.brand.green} 0%, ${colors.brand.blue} 100%)`;
										e.currentTarget.style.transform = 'scale(1.1)';
										e.currentTarget.style.borderColor = colors.brand.green;
									}
								}}
								onMouseLeave={(e) => {
									if (newMessage && newMessage.trim() && !isSending) {
										e.currentTarget.style.background = `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.supporting.lightBlue} 100%)`;
										e.currentTarget.style.transform = 'scale(1)';
										e.currentTarget.style.borderColor = colors.brand.blue;
									}
								}}>
								{isSending ? (
									<div className='w-4 h-4 border-2 border-white/30 border-t-white animate-spin' />
								) : (
									<svg
										width='18'
										height='18'
										viewBox='0 0 24 24'
										fill='none'
										stroke='currentColor'
										strokeWidth='2'
										strokeLinecap='round'
										strokeLinejoin='round'>
										<path d='M3.714 3.048a.498.498 0 0 0-.683.627l2.843 7.627a2 2 0 0 1 0 1.396l-2.842 7.627a.498.498 0 0 0 .682.627l18-8.5a.5.5 0 0 0 0-.904z' />
										<path d='M6 12h16' />
									</svg>
								)}
							</button>
						</div>
					</div>
				</div>

				{/* Credit Status */}
				<div className='mt-2 flex justify-center'>
					<CreditStatus compact={true} />
				</div>
			</div>
		</div>
	);
};

export default InputArea;
