/** @format */

import { CreditsDisplay } from '@/app/shared/credits';
import { zIndexLayers } from 'app/chat/styles';
import { TopBarProps } from 'app/chat/types';
import { colors } from 'app/colors';
import { useSession } from 'next-auth/react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import React, { useRef, useState } from 'react';
import {
	FaBars,
	FaEllipsisV,
	FaGlobe,
	FaMap,
	FaMapMarkerAlt,
} from 'react-icons/fa';
import UserDropdown from './UserDropdown';

const TopBar: React.FC<TopBarProps> = ({
	isLeftOpen,
	isRightOpen,
	setIsLeftOpen,
	setIsRightOpen,
	startNewChat,
	userLocation,
	locationError,
	locationLoading,
	requestAutoLocation,
}) => {
	const [dropdownOpen, setDropdownOpen] = useState(false);
	const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
	const { data: session } = useSession();
	const router = useRouter();
	const buttonRef = useRef<HTMLDivElement>(null);
	const mobileMenuRef = useRef<HTMLDivElement>(null);

	const getUserInitials = () => {
		if (session?.user?.username) {
			return session.user.username.substring(0, 2).toUpperCase();
		}
		if (session?.user?.email) {
			return session.user.email.substring(0, 2).toUpperCase();
		}
		return 'U';
	};

	// Handle click outside mobile menu
	React.useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (
				mobileMenuRef.current &&
				!mobileMenuRef.current.contains(event.target as Node) &&
				mobileMenuOpen
			) {
				setMobileMenuOpen(false);
			}
		};

		if (mobileMenuOpen) {
			document.addEventListener('mousedown', handleClickOutside);
			return () =>
				document.removeEventListener('mousedown', handleClickOutside);
		}
	}, [mobileMenuOpen]);

	return (
		<div
			className={`h-16 px-4 md:px-6 lg:px-12 flex items-center justify-between relative ${zIndexLayers.topBar} shadow-sm`}
			style={{
				background: `linear-gradient(135deg, #F0FAFF 0%, #F5FDF8 100%)`,
				borderBottom: `1px solid ${colors.ui.gray200}`,
				boxSizing: 'border-box',
				height: '64px',
				minHeight: '64px',
				maxHeight: '64px',
				overflow: 'hidden',
			}}>
			<div className='flex items-center gap-3'>
				{/* Logo - only show when left sidebar is closed */}
				{!isLeftOpen && (
					<div
						className='flex items-center cursor-pointer h-full flex-shrink-0'
						onClick={() => router.push('/')}>
						<div className='flex items-center justify-center w-10 h-10'>
							<Image
								src='/logo/512x512.png'
								alt='Wizlop Logo'
								width={40}
								height={40}
								className='object-contain'
								priority
							/>
						</div>
					</div>
				)}

				{!isLeftOpen && (
					<button
						onClick={() => setIsLeftOpen(true)}
						className='p-2 transition-colors border'
						style={{
							color: colors.neutral.slateGray,
							borderColor: colors.ui.gray200,
							backgroundColor: 'transparent',
							boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)',
						}}
						onMouseEnter={(e) => {
							e.currentTarget.style.color = colors.brand.green;
							e.currentTarget.style.backgroundColor = colors.ui.green100;
							e.currentTarget.style.boxShadow = `0 4px 12px ${colors.ui.green200}40`;
						}}
						onMouseLeave={(e) => {
							e.currentTarget.style.color = colors.neutral.slateGray;
							e.currentTarget.style.backgroundColor = 'transparent';
							e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.05)';
						}}>
						<FaBars />
					</button>
				)}

				{/* New Chat button - only show when left sidebar is closed */}
				{!isLeftOpen && (
					<button
						onClick={startNewChat}
						className='px-3 py-1.5 text-sm font-medium transition-colors border'
						style={{
							backgroundColor: colors.brand.blue,
							color: 'white',
							borderColor: colors.brand.blue,
							boxShadow: `0 2px 8px ${colors.brand.blue}40`,
						}}
						onMouseEnter={(e) => {
							e.currentTarget.style.backgroundColor =
								colors.supporting.lightBlue;
							e.currentTarget.style.boxShadow = `0 4px 12px ${colors.supporting.lightBlue}40`;
						}}
						onMouseLeave={(e) => {
							e.currentTarget.style.backgroundColor = colors.brand.blue;
							e.currentTarget.style.boxShadow = `0 2px 8px ${colors.brand.blue}40`;
						}}>
						New Chat
					</button>
				)}
			</div>

			{/* Center section - can be used for additional navigation if needed */}
			<div className='flex-1 flex justify-center'>
				{/* Space for future navigation elements */}
			</div>

			<div className='flex items-center gap-2 md:gap-4 lg:gap-6'>
				{/* Credits Display - hidden on mobile, visible on md+ */}
				<div className='hidden md:block'>
					<CreditsDisplay
						size='small'
						showAddButton={false}
					/>
				</div>

				{/* Desktop Navigation - hidden on mobile */}
				<div className='hidden md:flex items-center gap-2 md:gap-3 lg:gap-4'>
					{/* Globe Navigation */}
					<button
						onClick={() => router.push('/globe')}
						className='flex items-center justify-center w-10 h-10 transition-colors border'
						style={{
							backgroundColor: colors.ui.green100,
							color: colors.brand.green,
							borderColor: colors.ui.green200,
							boxShadow: `0 2px 8px ${colors.ui.green200}40`,
						}}
						title='Interactive Globe'
						onMouseEnter={(e) => {
							e.currentTarget.style.backgroundColor = colors.ui.green200;
							e.currentTarget.style.boxShadow = `0 4px 12px ${colors.ui.green200}60`;
						}}
						onMouseLeave={(e) => {
							e.currentTarget.style.backgroundColor = colors.ui.green100;
							e.currentTarget.style.boxShadow = `0 2px 8px ${colors.ui.green200}40`;
						}}>
						<FaGlobe className='w-4 h-4' />
					</button>

					{/* POI Navigation */}
					<button
						onClick={() => router.push('/pois')}
						className='flex items-center justify-center w-10 h-10 transition-colors border'
						style={{
							backgroundColor: colors.ui.gray100,
							color: colors.supporting.teal,
							borderColor: colors.ui.gray200,
							boxShadow: `0 2px 8px ${colors.ui.gray200}40`,
						}}
						title='Points of Interest'
						onMouseEnter={(e) => {
							e.currentTarget.style.backgroundColor = colors.ui.gray200;
							e.currentTarget.style.boxShadow = `0 4px 12px ${colors.ui.gray200}60`;
						}}
						onMouseLeave={(e) => {
							e.currentTarget.style.backgroundColor = colors.ui.gray100;
							e.currentTarget.style.boxShadow = `0 2px 8px ${colors.ui.gray200}40`;
						}}>
						<FaMapMarkerAlt className='w-4 h-4' />
					</button>

					{/* Map Panel Toggle */}
					{!isRightOpen && (
						<button
							onClick={() => setIsRightOpen(true)}
							className='flex items-center justify-center w-10 h-10 transition-colors border'
							style={{
								backgroundColor: colors.ui.blue100,
								color: colors.brand.blue,
								borderColor: colors.ui.blue200,
								boxShadow: `0 2px 8px ${colors.ui.blue200}40`,
							}}
							title='Open Map Panel'
							onMouseEnter={(e) => {
								e.currentTarget.style.backgroundColor = colors.ui.blue200;
								e.currentTarget.style.boxShadow = `0 4px 12px ${colors.ui.blue200}60`;
							}}
							onMouseLeave={(e) => {
								e.currentTarget.style.backgroundColor = colors.ui.blue100;
								e.currentTarget.style.boxShadow = `0 2px 8px ${colors.ui.blue200}40`;
							}}>
							<FaMap className='w-4 h-4' />
						</button>
					)}
				</div>

				{/* Mobile Menu Button - visible only on mobile */}
				<div className='md:hidden relative'>
					<button
						onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
						className='flex items-center justify-center w-10 h-10 transition-all duration-200 border'
						style={{
							backgroundColor: colors.ui.gray100,
							color: colors.neutral.slateGray,
							borderColor: colors.ui.gray200,
							boxShadow: `0 2px 8px ${colors.ui.gray200}40`,
						}}
						title='More options'
						onMouseEnter={(e) => {
							e.currentTarget.style.backgroundColor = colors.ui.gray200;
							e.currentTarget.style.boxShadow = `0 4px 12px ${colors.ui.gray200}60`;
						}}
						onMouseLeave={(e) => {
							e.currentTarget.style.backgroundColor = colors.ui.gray100;
							e.currentTarget.style.boxShadow = `0 2px 8px ${colors.ui.gray200}40`;
						}}>
						<FaEllipsisV className='w-4 h-4' />
					</button>

					{/* Mobile Dropdown Menu */}
					{mobileMenuOpen && (
						<div
							ref={mobileMenuRef}
							className='absolute right-0 top-full mt-2 w-48 shadow-xl border z-50'
							style={{
								backgroundColor: 'rgba(255, 255, 255, 0.98)',
								borderColor: colors.ui.gray200,
								backdropFilter: 'blur(12px)',
								boxShadow: '0 8px 32px rgba(0, 0, 0, 0.15)',
							}}>
							<div className='p-2 space-y-1'>
								{/* Credits Display for mobile */}
								<div
									className='mb-3 p-2 border'
									style={{
										backgroundColor: colors.ui.blue50,
										borderColor: colors.ui.blue200,
									}}>
									<CreditsDisplay
										size='small'
										showAddButton={false}
									/>
								</div>
								<button
									onClick={() => {
										router.push('/globe');
										setMobileMenuOpen(false);
									}}
									className='w-full flex items-center gap-3 px-3 py-2 text-left text-sm transition-colors border'
									style={{
										color: colors.neutral.textBlack,
										fontFamily:
											'"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif',
										borderColor: 'transparent',
										backgroundColor: 'transparent',
									}}
									onMouseEnter={(e) => {
										e.currentTarget.style.backgroundColor = colors.ui.green100;
										e.currentTarget.style.borderColor = colors.ui.green200;
									}}
									onMouseLeave={(e) => {
										e.currentTarget.style.backgroundColor = 'transparent';
										e.currentTarget.style.borderColor = 'transparent';
									}}>
									<FaGlobe
										className='w-4 h-4'
										style={{ color: colors.brand.green }}
									/>
									Interactive Globe
								</button>
								<button
									onClick={() => {
										router.push('/pois');
										setMobileMenuOpen(false);
									}}
									className='w-full flex items-center gap-3 px-3 py-2 text-left text-sm transition-colors border'
									style={{
										color: colors.neutral.textBlack,
										fontFamily:
											'"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif',
										borderColor: 'transparent',
										backgroundColor: 'transparent',
									}}
									onMouseEnter={(e) => {
										e.currentTarget.style.backgroundColor = colors.ui.gray100;
										e.currentTarget.style.borderColor = colors.ui.gray200;
									}}
									onMouseLeave={(e) => {
										e.currentTarget.style.backgroundColor = 'transparent';
										e.currentTarget.style.borderColor = 'transparent';
									}}>
									<FaMapMarkerAlt
										className='w-4 h-4'
										style={{ color: colors.supporting.teal }}
									/>
									Points of Interest
								</button>
								{!isRightOpen && (
									<button
										onClick={() => {
											setIsRightOpen(true);
											setMobileMenuOpen(false);
										}}
										className='w-full flex items-center gap-3 px-3 py-2 text-left text-sm transition-colors border'
										style={{
											color: colors.neutral.textBlack,
											fontFamily:
												'"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif',
											borderColor: 'transparent',
											backgroundColor: 'transparent',
										}}
										onMouseEnter={(e) => {
											e.currentTarget.style.backgroundColor = colors.ui.blue100;
											e.currentTarget.style.borderColor = colors.ui.blue200;
										}}
										onMouseLeave={(e) => {
											e.currentTarget.style.backgroundColor = 'transparent';
											e.currentTarget.style.borderColor = 'transparent';
										}}>
										<FaMap
											className='w-4 h-4'
											style={{ color: colors.brand.blue }}
										/>
										Open Map Panel
									</button>
								)}
							</div>
						</div>
					)}
				</div>

				{/* User Dropdown */}
				<div className='relative'>
					<div
						ref={buttonRef}
						onClick={() => setDropdownOpen((prev) => !prev)}
						className='w-10 h-10 flex items-center justify-center cursor-pointer select-none transition-all duration-200 hover:scale-105 border'
						style={{
							background: `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.navy} 100%)`,
							color: 'white',
							fontFamily:
								'"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif',
							borderColor: colors.brand.navy,
							boxShadow: `0 2px 8px ${colors.brand.blue}40`,
							fontWeight: '600',
							fontSize: '12px',
						}}>
						{getUserInitials()}
					</div>

					<UserDropdown
						isOpen={dropdownOpen}
						onClose={() => setDropdownOpen(false)}
						buttonRef={buttonRef}
						userLocation={userLocation}
						locationError={locationError}
						locationLoading={locationLoading}
						requestAutoLocation={requestAutoLocation}
					/>
				</div>
			</div>
		</div>
	);
};

export default TopBar;
